import { NextResponse } from 'next/server';
import { generateObject } from 'ai';
import { codeOcrAgent } from './agent';
import { CodeOcrRequestSchema, CodeOcrResponseSchema } from '@/lib/schemas';
import { openai } from '@ai-sdk/openai';

export const runtime = 'edge';

const CODE_OCR_PROMPT_TEMPLATE = """
You are an expert AI assistant specializing in Optical Code Recognition (OCR).
Your task is to accurately extract source code from a given image.

**Instructions:**

1.  **Analyze the Image:** Carefully examine the user-provided image to
    identify any blocks of source code.
2.  **Extract the Code:** Transcribe the code text exactly as you see it,
    preserving original indentation, spacing, and line breaks.
3.  **Identify the Language:** Determine the programming language of the
    extracted code.
4.  **Format the Output:** You MUST format your response as a single
    Markdown code block that conforms to the required structure.

**Output Format Rules (Strictly Enforced):**

*   Your entire response MUST be a single string that will be parsed into
    a JSON object with a "code" field.
*   The value of the "code" field MUST start with three backticks (```).
*   Immediately following the opening backticks, you MUST specify the
    detected programming language (e.g., `python`, `javascript`, `java`).
*   If you are unsure of the programming language or if it's plain text,
    you MUST use the identifier `text`.
*   The language identifier MUST be followed by a newline character (`\n`).
*   After the newline, insert the extracted code.
*   The string MUST end with three backticks (```).

**Example of the final string value for the 'code' field:**
```python
def hello_world():
    print("Hello, World!")
```

**Final Check:** Before providing the response, double-check that it
strictly adheres to the ` ```language\n...code...\n``` ` format.
Your response will be programmatically validated.
`;

export async function POST(req: Request) {
  try {
    // 1. Validate request body
    const body = await req.json();
    const validation = CodeOcrRequestSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { image_source, model_name, api_key, api_base } = validation.data;

    // 2. Dynamically configure the AI model based on user input
    const model = openai(model_name, {
      apiKey: api_key || process.env.OPENAI_API_KEY,
      baseURL: api_base,
    });

    // 3. Use the Vercel AI SDK's `generateObject`
    const { object } = await generateObject({
      model: model,
      schema: CodeOcrResponseSchema,
      messages: [
        {
          role: 'user',
          content: [
            { type: 'text', text: CODE_OCR_PROMPT_TEMPLATE },
            { type: 'image_url', image_url: { url: image_source } },
          ],
        },
      ],
    });

    // 4. Return the structured, validated response
    return NextResponse.json(object);

  } catch (error: any) {
    console.error('Error in CodeOCR API route:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred.', details: error.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
"""
